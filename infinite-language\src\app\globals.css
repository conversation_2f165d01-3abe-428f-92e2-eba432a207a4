@import "tailwindcss";

:root {
  /* 60-30-10 Color Scheme - New Warm Palette */
  --primary-60: #F7F2E5;     /* Cream/Beige - 60% dominant background */
  --secondary-30: #F97350;   /* Coral/Orange - 30% secondary */
  --accent-10: #B7AC9A;      /* Taupe/Brown - 10% accent */
  --highlight: #FAD06C;      /* Yellow - highlight color */

  /* Variations for better UX */
  --primary-light: #FEFCF8;
  --primary-dark: #F0E8D6;
  --secondary-light: #FB8A6B;
  --secondary-dark: #E85A35;
  --accent-light: #C4B8A7;
  --accent-dark: #A39485;
  --highlight-light: #FBD885;
  --highlight-dark: #F8C653;

  /* Text colors */
  --text-primary: #2D2A26;
  --text-secondary: #5A5651;
  --text-light: #8B8680;

  /* Legacy variables for compatibility */
  --background: var(--primary-60);
  --foreground: var(--text-primary);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Custom color palette */
  --color-primary-60: var(--primary-60);
  --color-secondary-30: var(--secondary-30);
  --color-accent-10: var(--accent-10);
  --color-highlight: var(--highlight);
  --color-primary-light: var(--primary-light);
  --color-primary-dark: var(--primary-dark);
  --color-secondary-light: var(--secondary-light);
  --color-secondary-dark: var(--secondary-dark);
  --color-accent-light: var(--accent-light);
  --color-accent-dark: var(--accent-dark);
  --color-highlight-light: var(--highlight-light);
  --color-highlight-dark: var(--highlight-dark);
}

@media (prefers-color-scheme: dark) {
  :root {
    --primary-60: #2D2A26;
    --primary-light: #3A3530;
    --primary-dark: #1F1D1A;
    --secondary-30: #F97350;
    --secondary-light: #FB8A6B;
    --secondary-dark: #E85A35;
    --text-primary: #F7F2E5;
    --text-secondary: #C4B8A7;
    --text-light: #8B8680;
    --background: var(--primary-60);
    --foreground: var(--text-primary);
  }
}

body {
  background: linear-gradient(135deg, var(--primary-60) 0%, var(--primary-light) 100%);
  color: var(--foreground);
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
}

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoom-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-from-top-2 {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Markdown content styling */
.markdown-content {
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.markdown-content p {
  margin-bottom: 0.5rem;
}

.markdown-content p:last-child {
  margin-bottom: 0;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 0.5rem;
}

.markdown-content li {
  margin-bottom: 0.25rem;
}

.markdown-content li:last-child {
  margin-bottom: 0;
}

.markdown-content code {
  font-size: 0.875rem;
}

.markdown-content pre {
  margin-bottom: 0.5rem;
}

.markdown-content blockquote {
  margin-bottom: 0.5rem;
}

@keyframes slide-in-from-bottom-4 {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(249, 115, 80, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(249, 115, 80, 0.6);
  }
}

.animate-in {
  animation-fill-mode: both;
}

.fade-in-0 {
  animation-name: fade-in;
}

.zoom-in-95 {
  animation-name: zoom-in;
}

.duration-200 {
  animation-duration: 200ms;
}

.slide-in-from-top-2 {
  animation-name: slide-in-from-top-2;
}

.slide-in-from-bottom-4 {
  animation-name: slide-in-from-bottom-4;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.duration-300 {
  animation-duration: 300ms;
}

.duration-500 {
  animation-duration: 500ms;
}

.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-1000 {
  animation-delay: 1000ms;
}

/* Glass morphism effect with new warm color palette */
.glass {
  background: rgba(247, 242, 229, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(247, 242, 229, 0.18);
}

.glass-secondary {
  background: rgba(249, 115, 80, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(249, 115, 80, 0.2);
}

.glass-accent {
  background: rgba(183, 172, 154, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(183, 172, 154, 0.2);
}

.glass-highlight {
  background: rgba(250, 208, 108, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(250, 208, 108, 0.2);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar with new warm colors */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--primary-light);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--secondary-30);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-dark);
}

/* Header spacing utility - ensures consistent spacing from fixed header */
.header-spacing {
  padding-top: 6rem; /* 96px - matches pt-24 */
}

/* Debug utility to visualize header spacing */
.debug-header-spacing {
  position: relative;
}

.debug-header-spacing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rem; /* Header height */
  background: rgba(255, 0, 0, 0.1);
  border-bottom: 2px solid red;
  z-index: 9999;
  pointer-events: none;
}

/* Quiz layout fixes */
.quiz-container {
  min-height: calc(100vh - 6rem); /* Account for header height + spacing */
}

.quiz-content {
  max-height: calc(100vh - 8rem); /* Account for header + padding */
}

/* Prevent header overflow on mobile */
.header-container {
  overflow: hidden;
}

.header-container > * {
  min-width: 0; /* Allow flex items to shrink */
}

/* 60-30-10 Color Utility Classes */
.bg-primary-60 {
  background-color: var(--primary-60);
}

.bg-primary-light {
  background-color: var(--primary-light);
}

.bg-primary-dark {
  background-color: var(--primary-dark);
}

.bg-secondary-30 {
  background-color: var(--secondary-30);
}

.bg-secondary-light {
  background-color: var(--secondary-light);
}

.bg-secondary-dark {
  background-color: var(--secondary-dark);
}

.bg-accent-10 {
  background-color: var(--accent-10);
}

.bg-accent-light {
  background-color: var(--accent-light);
}

.bg-accent-dark {
  background-color: var(--accent-dark);
}

.bg-highlight {
  background-color: var(--highlight);
}

.bg-highlight-light {
  background-color: var(--highlight-light);
}

.bg-highlight-dark {
  background-color: var(--highlight-dark);
}

.text-primary-60 {
  color: var(--primary-60);
}

.text-secondary-30 {
  color: var(--secondary-30);
}

.text-accent-10 {
  color: var(--accent-10);
}

.text-highlight {
  color: var(--highlight);
}

.border-primary-60 {
  border-color: var(--primary-60);
}

.border-secondary-30 {
  border-color: var(--secondary-30);
}

.border-accent-10 {
  border-color: var(--accent-10);
}

.border-highlight {
  border-color: var(--highlight);
}

/* Gradient combinations using new warm palette */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-60) 0%, var(--primary-light) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-30) 0%, var(--secondary-light) 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, var(--accent-10) 0%, var(--accent-light) 100%);
}

.gradient-highlight {
  background: linear-gradient(135deg, var(--highlight) 0%, var(--highlight-light) 100%);
}

.gradient-primary-secondary {
  background: linear-gradient(135deg, var(--primary-60) 0%, var(--secondary-30) 100%);
}

.gradient-secondary-accent {
  background: linear-gradient(135deg, var(--secondary-30) 0%, var(--accent-10) 100%);
}

.gradient-warm-sunset {
  background: linear-gradient(135deg, var(--secondary-30) 0%, var(--highlight) 50%, var(--primary-60) 100%);
}

.gradient-warm-earth {
  background: linear-gradient(135deg, var(--accent-10) 0%, var(--primary-60) 100%);
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_LevelSelector__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LevelSelector */ \"(app-pages-browser)/./src/components/LevelSelector.tsx\");\n/* harmony import */ var _components_QuestionCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/QuestionCard */ \"(app-pages-browser)/./src/components/QuestionCard.tsx\");\n/* harmony import */ var _components_ResultCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ResultCard */ \"(app-pages-browser)/./src/components/ResultCard.tsx\");\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/LoadingSpinner */ \"(app-pages-browser)/./src/components/LoadingSpinner.tsx\");\n/* harmony import */ var _components_AuthModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/AuthModal */ \"(app-pages-browser)/./src/components/AuthModal.tsx\");\n/* harmony import */ var _components_HistoryPage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/HistoryPage */ \"(app-pages-browser)/./src/components/HistoryPage.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { language } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [quizState, setQuizState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentQuestion: null,\n        selectedAnswer: null,\n        showResult: false,\n        score: {\n            correct: 0,\n            total: 0\n        },\n        level: null,\n        language: 'en'\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHistory, setShowHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionStartTime, setQuestionStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const generateQuestion = async (level)=>{\n        // Clear current question immediately to prevent showing old content\n        setQuizState((prev)=>({\n                ...prev,\n                currentQuestion: null,\n                selectedAnswer: null,\n                showResult: false\n            }));\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/generate-question', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    level,\n                    language\n                })\n            });\n            const data = await response.json();\n            if (data.success && data.question) {\n                setQuizState((prev)=>({\n                        ...prev,\n                        currentQuestion: data.question,\n                        selectedAnswer: null,\n                        showResult: false,\n                        level,\n                        language\n                    }));\n                setQuestionStartTime(Date.now());\n            } else {\n                console.error('Failed to generate question:', data.error);\n            }\n        } catch (error) {\n            console.error('Error fetching question:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleLevelSelect = (level)=>{\n        // Navigate to SSR quiz page using Next.js router\n        router.push(\"/quiz/\".concat(level, \"?lang=\").concat(language));\n    };\n    const handleAnswerSelect = async (answerIndex)=>{\n        if (quizState.showResult || !quizState.currentQuestion) return;\n        setQuizState((prev)=>({\n                ...prev,\n                selectedAnswer: answerIndex\n            }));\n        // Submit answer to server for validation\n        try {\n            const response = await fetch('/api/submit-answer', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    questionId: quizState.currentQuestion.id,\n                    userAnswer: answerIndex\n                })\n            });\n            const data = await response.json();\n            if (data.success && data.result) {\n                const { isCorrect, correctAnswer, explanation } = data.result;\n                const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);\n                // Update quiz state with result\n                setQuizState((prev)=>({\n                        ...prev,\n                        showResult: true,\n                        currentQuestion: prev.currentQuestion ? {\n                            ...prev.currentQuestion,\n                            correctAnswer,\n                            explanation\n                        } : null,\n                        score: {\n                            correct: prev.score.correct + (isCorrect ? 1 : 0),\n                            total: prev.score.total + 1\n                        }\n                    }));\n                // Save to history if user is logged in\n                if (user && quizState.currentQuestion) {\n                    try {\n                        await fetch('/api/quiz/history', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                questionId: quizState.currentQuestion.id,\n                                question: quizState.currentQuestion.question,\n                                options: quizState.currentQuestion.options,\n                                correctAnswer: correctAnswer,\n                                userAnswer: answerIndex,\n                                explanation: explanation,\n                                level: quizState.currentQuestion.level,\n                                language: language,\n                                timeSpent: timeSpent\n                            })\n                        });\n                    } catch (error) {\n                        console.error('Failed to save quiz history:', error);\n                    }\n                }\n            } else {\n                console.error('Failed to submit answer:', data.error);\n            }\n        } catch (error) {\n            console.error('Error submitting answer:', error);\n        }\n    };\n    const handleNextQuestion = ()=>{\n        if (quizState.level) {\n            generateQuestion(quizState.level);\n        }\n    };\n    const handleBackToLevels = ()=>{\n        setQuizState({\n            currentQuestion: null,\n            selectedAnswer: null,\n            showResult: false,\n            score: {\n                correct: 0,\n                total: 0\n            },\n            level: null,\n            language\n        });\n    };\n    if (showHistory) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HistoryPage__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            onBack: ()=>setShowHistory(false)\n        }, void 0, false, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 172,\n            columnNumber: 12\n        }, this);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 176,\n            columnNumber: 12\n        }, this);\n    }\n    if (!quizState.level) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    onAuthClick: ()=>setShowAuthModal(true),\n                    onHistoryClick: ()=>setShowHistory(true)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LevelSelector__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onLevelSelect: handleLevelSelect,\n                        onAuthClick: ()=>setShowAuthModal(true),\n                        onHistoryClick: ()=>setShowHistory(true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    isOpen: showAuthModal,\n                    onClose: ()=>setShowAuthModal(false)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    if (quizState.currentQuestion && quizState.showResult) {\n        const isCorrect = quizState.selectedAnswer === quizState.currentQuestion.correctAnswer;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    onAuthClick: ()=>setShowAuthModal(true),\n                    onHistoryClick: ()=>setShowHistory(true),\n                    showBackButton: true,\n                    onBackClick: handleBackToLevels,\n                    showScore: true,\n                    score: quizState.score\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResultCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        question: quizState.currentQuestion,\n                        selectedAnswer: quizState.selectedAnswer,\n                        isCorrect: isCorrect,\n                        onNextQuestion: handleNextQuestion,\n                        score: quizState.score\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    isOpen: showAuthModal,\n                    onClose: ()=>setShowAuthModal(false)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    if (quizState.currentQuestion) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    onAuthClick: ()=>setShowAuthModal(true),\n                    onHistoryClick: ()=>setShowHistory(true),\n                    showBackButton: true,\n                    onBackClick: handleBackToLevels,\n                    showScore: true,\n                    score: quizState.score\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuestionCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        question: quizState.currentQuestion,\n                        selectedAnswer: quizState.selectedAnswer,\n                        onAnswerSelect: handleAnswerSelect,\n                        showResult: quizState.showResult\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    isOpen: showAuthModal,\n                    onClose: ()=>setShowAuthModal(false)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                onAuthClick: ()=>setShowAuthModal(true),\n                onHistoryClick: ()=>setShowHistory(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LevelSelector__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    onLevelSelect: handleLevelSelect,\n                    onAuthClick: ()=>setShowAuthModal(true),\n                    onHistoryClick: ()=>setShowHistory(true)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"uzJE5sdzMP4BawJAYMDLWNgA4YI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LoadingButton.tsx":
/*!******************************************!*\
  !*** ./src/components/LoadingButton.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction LoadingButton(param) {\n    let { isLoading = false, loadingText, children, variant = 'primary', size = 'md', disabled, className = '', ...props } = param;\n    const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    const variantClasses = {\n        primary: 'gradient-secondary hover:bg-secondary-dark text-white shadow-lg hover:shadow-xl focus:ring-secondary-30',\n        secondary: 'gradient-highlight hover:bg-highlight-dark text-white shadow-lg hover:shadow-xl focus:ring-highlight',\n        outline: 'border-2 border-secondary-30 text-secondary-30 hover:bg-secondary-30 hover:text-white focus:ring-secondary-30'\n    };\n    const sizeClasses = {\n        sm: 'px-4 py-2 text-sm',\n        md: 'px-6 py-3 text-base',\n        lg: 'px-8 py-4 text-lg'\n    };\n    const isDisabled = disabled || isLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ...props,\n        disabled: isDisabled,\n        className: \"\".concat(baseClasses, \" \").concat(variantClasses[variant], \" \").concat(sizeClasses[size], \" \").concat(className),\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mr-2 flex items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LoadingButton.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LoadingButton.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: isLoading ? 'opacity-75' : '',\n                children: isLoading && loadingText ? loadingText : children\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LoadingButton.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LoadingButton.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_c = LoadingButton;\nvar _c;\n$RefreshReg$(_c, \"LoadingButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LoadingButton.tsx\n"));

/***/ })

});
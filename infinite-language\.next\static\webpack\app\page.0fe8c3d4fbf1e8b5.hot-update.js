"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _contexts_DropdownContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/DropdownContext */ \"(app-pages-browser)/./src/contexts/DropdownContext.tsx\");\n/* harmony import */ var _LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./LanguageSwitcher */ \"(app-pages-browser)/./src/components/LanguageSwitcher.tsx\");\n/* harmony import */ var _UserMenu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./UserMenu */ \"(app-pages-browser)/./src/components/UserMenu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Header(param) {\n    let { onAuthClick, onHistoryClick, showBackButton = false, onBackClick, showScore = false, score, onResetScore } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleScroll = {\n                \"Header.useEffect.handleScroll\": ()=>{\n                    const scrollTop = window.scrollY;\n                    setIsScrolled(scrollTop > 20);\n                }\n            }[\"Header.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Header.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_DropdownContext__WEBPACK_IMPORTED_MODULE_4__.DropdownProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-500 \".concat(isScrolled ? 'glass backdrop-blur-md shadow-lg shadow-black/5' : 'bg-transparent'),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\",\n                            children: [\n                                showBackButton && onBackClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onBackClick,\n                                    className: \"group flex items-center space-x-2 px-4 py-2.5 rounded-xl transition-all duration-500 shadow-sm hover:shadow-md \".concat(isScrolled ? 'bg-white/80 hover:bg-white/90 text-gray-700 hover:text-gray-900' : 'bg-white/80 hover:bg-white/90 text-gray-700 hover:text-gray-900 backdrop-blur-sm'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 transition-transform group-hover:-translate-x-0.5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-semibold\",\n                                            children: t('backToLevels')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 min-w-0 flex-1 sm:flex-initial\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 relative flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/logo.svg\",\n                                                alt: \"Infinite English - Học Tiếng Anh với AI\",\n                                                className: \"w-full h-full object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden sm:block min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold transition-all duration-500 \".concat(isScrolled ? 'text-secondary-30' : 'text-secondary-dark drop-shadow-lg'),\n                                                    children: \"Infinite English\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs font-medium transition-all duration-500 max-w-xs truncate \".concat(isScrolled ? 'text-gray-500' : 'text-gray-600 drop-shadow-md'),\n                                                    children: t('appSubtitle')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"sm:hidden min-w-0 flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-lg font-bold transition-all duration-500 truncate \".concat(isScrolled ? 'text-secondary-30' : 'text-secondary-dark drop-shadow-lg'),\n                                                children: \"Infinite English\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex items-center space-x-1 ml-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-700 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"Quiz\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/lessons\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-700 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"AI Lessons\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/reading\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-700 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"Reading\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/tutor\",\n                                            className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 \".concat(isScrolled ? 'text-gray-700 hover:text-secondary-30 hover:bg-secondary-30/10' : 'text-gray-700 hover:text-secondary-30 hover:bg-white/50'),\n                                            children: \"AI Tutor\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        showScore && score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:flex items-center space-x-3 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 px-3 py-2 rounded-xl shadow-sm transition-all duration-500 \".concat(isScrolled ? 'bg-gradient-to-r from-secondary-30/10 to-highlight/10 text-secondary-dark' : 'bg-gradient-to-r from-secondary-30/10 to-highlight/10 text-secondary-dark backdrop-blur-sm'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-6 rounded-full flex items-center justify-center transition-all duration-500 \".concat(isScrolled ? 'bg-secondary-30/20' : 'bg-secondary-30/20'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3 h-3 transition-colors duration-500 \".concat(isScrolled ? 'text-emerald-600' : 'text-emerald-600'),\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold\",\n                                                    children: [\n                                                        t('score'),\n                                                        \": \",\n                                                        score.correct,\n                                                        \"/\",\n                                                        score.total\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs transition-colors duration-500 \".concat(isScrolled ? 'text-emerald-600' : 'text-emerald-600'),\n                                                    children: [\n                                                        score.total > 0 ? Math.round(score.correct / score.total * 100) : 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this),\n                                onResetScore && score.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onResetScore,\n                                    className: \"group p-2 rounded-lg transition-all duration-500 shadow-sm hover:shadow-md \".concat(isScrolled ? 'bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700' : 'bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700'),\n                                    title: t('resetScore') || 'Reset Score',\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this),\n                        showScore && score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sm:hidden flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 px-2 py-1 rounded-lg text-xs font-bold transition-all duration-500 \".concat(isScrolled ? 'bg-gradient-to-r from-emerald-50 to-blue-50 text-emerald-700' : 'bg-gradient-to-r from-emerald-50 to-blue-50 text-emerald-700 backdrop-blur-sm'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        score.correct,\n                                        \"/\",\n                                        score.total\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    isScrolled: isScrolled\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this),\n                                user ? onHistoryClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserMenu__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    onHistoryClick: onHistoryClick\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 33\n                                }, this) : onAuthClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onAuthClick,\n                                    className: \"group relative px-6 py-2.5 rounded-xl transition-all duration-500 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 \".concat(isScrolled ? 'gradient-secondary hover:bg-secondary-dark text-white' : 'gradient-secondary hover:bg-secondary-dark text-white backdrop-blur-sm'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10\",\n                                            children: t('login')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, this),\n                                        isScrolled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"RqXGv3cvLAemF5k+prPSIEqRBbQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Header.tsx\n"));

/***/ })

});
'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Level } from '@/types';
import { useLanguage } from '@/contexts/LanguageContext';

interface LevelSelectorProps {
  onLevelSelect: (level: Level) => void;
  onAuthClick?: () => void;
  onHistoryClick?: () => void;
}

export default function LevelSelector({ onLevelSelect }: LevelSelectorProps) {
  const { t } = useLanguage();
  const [loadingLevel, setLoadingLevel] = useState<Level | null>(null);

  const levels = [
    {
      id: 'beginner' as Level,
      title: t('beginner'),
      description: t('beginnerDesc'),
      color: 'gradient-secondary',
      hoverColor: 'bg-secondary-dark',
      icon: '🌱',
      gradient: '',
      accentColor: 'secondary-30',
      textColor: 'text-white'
    },
    {
      id: 'intermediate' as Level,
      title: t('intermediate'),
      description: t('intermediateDesc'),
      color: 'gradient-highlight',
      hoverColor: 'bg-highlight-dark',
      icon: '🌿',
      gradient: '',
      accentColor: 'highlight',
      textColor: 'text-white'
    },
    {
      id: 'advanced' as Level,
      title: t('advanced'),
      description: t('advancedDesc'),
      color: 'gradient-accent',
      hoverColor: 'bg-accent-dark',
      icon: '🌳',
      gradient: '',
      accentColor: 'accent-10',
      textColor: 'text-white'
    }
  ];

  const handleLevelClick = async (level: Level) => {
    setLoadingLevel(level);
    await new Promise(resolve => setTimeout(resolve, 300));
    onLevelSelect(level);
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Artistic Background */}
      <div className="absolute inset-0">
        {/* Base gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900"></div>

        {/* Artistic shapes */}
        <div className="absolute top-0 left-0 w-full h-full">
          {/* Large geometric shapes */}
          <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-br from-pink-500/20 to-purple-600/20 rounded-full blur-3xl transform rotate-12"></div>
          <div className="absolute top-40 right-20 w-80 h-80 bg-gradient-to-br from-blue-400/25 to-cyan-500/25 rounded-full blur-2xl"></div>
          <div className="absolute bottom-20 left-1/4 w-72 h-72 bg-gradient-to-br from-yellow-400/15 to-orange-500/15 rounded-full blur-3xl transform -rotate-12"></div>

          {/* Floating geometric elements */}
          <div className="absolute top-1/4 left-1/3 w-32 h-32 bg-gradient-to-br from-emerald-400/30 to-teal-500/30 transform rotate-45 rounded-2xl blur-xl"></div>
          <div className="absolute bottom-1/3 right-1/4 w-24 h-24 bg-gradient-to-br from-rose-400/25 to-pink-500/25 transform rotate-12 rounded-full blur-lg"></div>

          {/* Abstract lines */}
          <svg className="absolute inset-0 w-full h-full opacity-10" viewBox="0 0 1000 1000">
            <path d="M100,200 Q300,100 500,200 T900,200" stroke="url(#gradient1)" strokeWidth="2" fill="none"/>
            <path d="M200,400 Q400,300 600,400 T1000,400" stroke="url(#gradient2)" strokeWidth="3" fill="none"/>
            <path d="M0,600 Q200,500 400,600 T800,600" stroke="url(#gradient3)" strokeWidth="2" fill="none"/>
            <defs>
              <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.5"/>
                <stop offset="100%" stopColor="#06B6D4" stopOpacity="0.5"/>
              </linearGradient>
              <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#F59E0B" stopOpacity="0.4"/>
                <stop offset="100%" stopColor="#EF4444" stopOpacity="0.4"/>
              </linearGradient>
              <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#10B981" stopOpacity="0.3"/>
                <stop offset="100%" stopColor="#3B82F6" stopOpacity="0.3"/>
              </linearGradient>
            </defs>
          </svg>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Hero Section - Compact */}
        <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-20">
          <div className="max-w-6xl mx-auto text-center">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 glass backdrop-blur-xl rounded-full text-sm font-medium text-white mb-8 shadow-2xl border border-white/20">
              <span className="w-3 h-3 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full mr-3 animate-pulse"></span>
              AI-Powered Learning Platform
            </div>

            {/* Main Title */}
            <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
              <span className="bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent">
                {t('appTitle')}
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl sm:text-2xl text-blue-100 mb-12 max-w-4xl mx-auto leading-relaxed">
              {t('appSubtitle')}
            </p>

            {/* Feature Pills */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              <div className="glass backdrop-blur-xl px-6 py-3 rounded-full border border-white/20 text-white">
                <span className="text-yellow-400 mr-2">∞</span>
                Unlimited Questions
              </div>
              <div className="glass backdrop-blur-xl px-6 py-3 rounded-full border border-white/20 text-white">
                <span className="text-green-400 mr-2">🧠</span>
                Smart AI Learning
              </div>
              <div className="glass backdrop-blur-xl px-6 py-3 rounded-full border border-white/20 text-white">
                <span className="text-blue-400 mr-2">🌐</span>
                Multi-Language
              </div>
            </div>

            {/* Banner Image - Floating */}
            <div className="relative mb-16">
              <div className="relative inline-block">
                <Image
                  src="/images/banner.png"
                  alt="AI English Learning Platform"
                  width={500}
                  height={350}
                  className="w-full max-w-lg h-auto object-cover rounded-3xl shadow-2xl animate-float"
                  priority
                />
                {/* Floating decorative elements */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full animate-bounce delay-300 shadow-lg"></div>
                <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full animate-bounce delay-700 shadow-lg"></div>
                <div className="absolute top-1/2 -left-8 w-4 h-4 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-full animate-pulse shadow-lg"></div>
                <div className="absolute top-1/4 -right-6 w-5 h-5 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full animate-pulse delay-500 shadow-lg"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Level Selection Cards - Artistic Design */}
        <div className="px-4 sm:px-6 lg:px-8 pb-20">
          <div className="max-w-5xl mx-auto">
            {/* Section Title */}
            <div className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
                Choose Your Learning Level
              </h2>
              <p className="text-xl text-blue-100 max-w-2xl mx-auto">
                Start your personalized English learning journey
              </p>
            </div>

            {/* Level Cards - Artistic Layout */}
            <div className="grid md:grid-cols-3 gap-8">
              {levels.map((level, index) => {
                const isLoading = loadingLevel === level.id;
                const gradients = [
                  'from-emerald-500 to-teal-600',
                  'from-blue-500 to-purple-600',
                  'from-orange-500 to-red-600'
                ];
                const glowColors = [
                  'shadow-emerald-500/25',
                  'shadow-blue-500/25',
                  'shadow-orange-500/25'
                ];

                return (
                  <button
                    key={level.id}
                    onClick={() => handleLevelClick(level.id)}
                    disabled={loadingLevel !== null}
                    className={`group relative bg-gradient-to-br ${gradients[index]} p-8 rounded-3xl shadow-2xl ${glowColors[index]} transform transition-all duration-700 hover:scale-110 hover:shadow-3xl hover:-translate-y-4 focus:outline-none focus:ring-4 focus:ring-white/20 disabled:opacity-75 disabled:cursor-not-allowed disabled:transform-none overflow-hidden`}
                  >
                    {/* Artistic background pattern */}
                    <div className="absolute inset-0 opacity-20">
                      <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full blur-2xl transform translate-x-8 -translate-y-8"></div>
                      <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full blur-xl transform -translate-x-4 translate-y-4"></div>
                    </div>

                    <div className="relative z-10 text-white text-center">
                      {/* Icon with artistic background */}
                      <div className="relative mb-6 inline-block">
                        <div className="w-20 h-20 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center transition-transform duration-500 group-hover:scale-125 group-hover:rotate-12">
                          <span className="text-4xl">{level.icon}</span>
                        </div>
                        {/* Floating dots around icon */}
                        <div className="absolute -top-2 -right-2 w-3 h-3 bg-white/60 rounded-full animate-pulse"></div>
                        <div className="absolute -bottom-2 -left-2 w-2 h-2 bg-white/40 rounded-full animate-pulse delay-300"></div>
                      </div>

                      <h3 className="text-2xl font-bold mb-4 transition-all duration-300 group-hover:scale-105">
                        {level.title}
                      </h3>

                      <p className="text-white/90 leading-relaxed mb-8 text-sm">
                        {level.description}
                      </p>

                      {/* Action Button */}
                      <div className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-xl rounded-xl transition-all duration-300 group-hover:bg-white/30 group-hover:scale-105">
                        {isLoading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                            <span className="text-sm font-medium">Loading...</span>
                          </>
                        ) : (
                          <>
                            <span className="text-sm font-medium">Start Learning</span>
                            <svg className="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                            </svg>
                          </>
                        )}
                      </div>
                    </div>

                    {/* Artistic corner elements */}
                    <div className="absolute top-4 right-4 w-6 h-6 bg-white/20 rounded-full opacity-60 group-hover:opacity-100 transition-opacity"></div>
                    <div className="absolute bottom-4 left-4 w-4 h-4 bg-white/20 rounded-full opacity-40 group-hover:opacity-80 transition-opacity"></div>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Quick Access Section - Compact */}
        <div className="px-4 sm:px-6 lg:px-8 pb-16">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Explore More Features
              </h2>
              <p className="text-xl text-blue-100">
                Discover additional learning tools and resources
              </p>
            </div>

            {/* Feature Grid - Compact */}
            <div className="grid md:grid-cols-2 gap-6">
              <a
                href="/lessons"
                className="group relative bg-gradient-to-br from-cyan-500 to-blue-600 rounded-3xl p-6 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-2 overflow-hidden"
              >
                <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10 text-white">
                  <div className="w-14 h-14 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center mb-4 transition-transform duration-300 group-hover:scale-110">
                    <span className="text-2xl">🎓</span>
                  </div>
                  <h3 className="text-xl font-bold mb-2">{t('aiLessons')}</h3>
                  <p className="text-cyan-100 text-sm mb-4">{t('lessonSubtitle')}</p>
                  <div className="inline-flex items-center text-sm font-medium">
                    <span>Start Learning</span>
                    <svg className="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                </div>
              </a>

              <a
                href="/reading"
                className="group relative bg-gradient-to-br from-purple-500 to-pink-600 rounded-3xl p-6 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-2 overflow-hidden"
              >
                <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10 text-white">
                  <div className="w-14 h-14 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center mb-4 transition-transform duration-300 group-hover:scale-110">
                    <span className="text-2xl">📖</span>
                  </div>
                  <h3 className="text-xl font-bold mb-2">{t('aiReading')}</h3>
                  <p className="text-purple-100 text-sm mb-4">{t('readingSubtitle')}</p>
                  <div className="inline-flex items-center text-sm font-medium">
                    <span>Start Reading</span>
                    <svg className="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                </div>
              </a>

              <a
                href="/tutor"
                className="group relative bg-gradient-to-br from-indigo-500 to-purple-600 rounded-3xl p-6 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-2 overflow-hidden md:col-span-2"
              >
                <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10 text-white text-center">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center mb-4 mx-auto transition-transform duration-300 group-hover:scale-110">
                    <span className="text-3xl">🤖</span>
                  </div>
                  <h3 className="text-2xl font-bold mb-2">AI English Tutor</h3>
                  <p className="text-indigo-100 text-sm mb-4 max-w-md mx-auto">
                    Get instant help with grammar, vocabulary, and pronunciation. Available 24/7 in Vietnamese.
                  </p>
                  <div className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-xl rounded-xl font-medium transition-all duration-300 group-hover:bg-white/30">
                    <span>Start Conversation</span>
                    <svg className="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ResultCard.tsx":
/*!***************************************!*\
  !*** ./src/components/ResultCard.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResultCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _MarkdownRenderer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MarkdownRenderer */ \"(app-pages-browser)/./src/components/MarkdownRenderer.tsx\");\n/* harmony import */ var _LoadingButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LoadingButton */ \"(app-pages-browser)/./src/components/LoadingButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ResultCard(param) {\n    let { question, selectedAnswer, isCorrect, onNextQuestion, score } = param;\n    _s();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [isLoadingNext, setIsLoadingNext] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const accuracy = score.total > 0 ? Math.round(score.correct / score.total * 100) : 0;\n    // Reset loading state if it takes too long\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResultCard.useEffect\": ()=>{\n            if (isLoadingNext) {\n                timeoutRef.current = setTimeout({\n                    \"ResultCard.useEffect\": ()=>{\n                        setIsLoadingNext(false);\n                    }\n                }[\"ResultCard.useEffect\"], 10000); // Reset after 10 seconds\n            }\n            return ({\n                \"ResultCard.useEffect\": ()=>{\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"ResultCard.useEffect\"];\n        }\n    }[\"ResultCard.useEffect\"], [\n        isLoadingNext\n    ]);\n    const handleNextQuestion = async ()=>{\n        setIsLoadingNext(true);\n        try {\n            await onNextQuestion();\n        } catch (error) {\n            console.error('Error loading next question:', error);\n            setIsLoadingNext(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center p-3 sm:p-4 quiz-container\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/95 backdrop-blur-xl rounded-sm shadow-2xl border border-white/20 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 sm:px-6 py-4 text-center relative overflow-hidden \".concat(isCorrect ? 'bg-gradient-to-br from-secondary-30/10 via-highlight/10 to-accent-10/10' : 'bg-gradient-to-br from-red-50/60 via-pink-50/60 to-orange-50/60'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center px-4 py-2 rounded-sm text-lg font-bold shadow-md border-2 backdrop-blur-sm \".concat(isCorrect ? 'bg-gradient-to-r from-secondary-30/20 to-highlight/20 text-secondary-dark border-secondary-30/30' : 'bg-gradient-to-r from-red-50 to-pink-50 text-red-700 border-red-200'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl mr-2\",\n                                            children: isCorrect ? '🎉' : '💪'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this),\n                                        isCorrect ? t('correct') : t('incorrect')\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/90 backdrop-blur-xl rounded-xl px-4 py-2 border border-white/30 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-emerald-600\",\n                                                            children: score.correct\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                            lineNumber: 84,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-600 text-xs font-medium\",\n                                                            children: t('correct')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-px h-6 bg-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-blue-600\",\n                                                            children: score.total\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-600 text-xs font-medium\",\n                                                            children: t('total')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-px h-6 bg-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold \".concat(accuracy >= 70 ? 'text-emerald-600' : accuracy >= 50 ? 'text-orange-600' : 'text-red-600'),\n                                                            children: [\n                                                                accuracy,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-600 text-xs font-medium\",\n                                                            children: t('accuracy')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 sm:p-6 quiz-content overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-gray-700 mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-br from-slate-400 to-slate-500 rounded-lg flex items-center justify-center mr-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            t('question')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-50/80 to-blue-50/80 backdrop-blur-sm rounded-xl p-3 border border-gray-200/50 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-800 text-sm leading-relaxed font-medium\",\n                                            children: question.question\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-gray-800 mb-2 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this),\n                                            t('answers')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-sm border-2 shadow-sm \".concat(isCorrect ? 'bg-gradient-to-r from-emerald-50/70 to-green-50/70 border-emerald-200' : 'bg-gradient-to-r from-red-50/70 to-pink-50/70 border-red-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 w-8 h-8 rounded-sm flex items-center justify-center text-white font-bold text-sm shadow-sm \".concat(isCorrect ? 'bg-gradient-to-br from-emerald-400 to-green-500' : 'bg-gradient-to-br from-red-400 to-pink-500'),\n                                                            children: isCorrect ? '✓' : '✗'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-bold text-gray-600 text-xs mb-1 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"mr-1\",\n                                                                            children: \"\\uD83D\\uDC64\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                                            lineNumber: 148,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        t('yourAnswer')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-semibold \".concat(isCorrect ? 'text-emerald-700' : 'text-red-700'),\n                                                                    children: selectedAnswer !== null ? question.options[selectedAnswer] : 'No answer selected'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                                    lineNumber: 151,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-xl border-2 bg-gradient-to-r from-emerald-50/70 to-green-50/70 border-emerald-200 shadow-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 w-8 h-8 bg-gradient-to-br from-emerald-400 to-green-500 rounded-lg flex items-center justify-center text-white font-bold text-sm shadow-sm\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-bold text-gray-600 text-xs mb-1 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"mr-1\",\n                                                                            children: \"\\uD83C\\uDFAF\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                                            lineNumber: 167,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        t('correctAnswer')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                                    lineNumber: 166,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-semibold text-emerald-700\",\n                                                                    children: question.correctAnswer !== undefined ? question.options[question.correctAnswer] : 'N/A'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-gray-700 mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-br from-slate-400 to-slate-500 rounded-lg flex items-center justify-center mr-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            t('explanation')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-slate-50/60 to-gray-50/60 backdrop-blur-sm border-l-4 border-slate-300 p-4 rounded-r-xl shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-6 h-6 bg-slate-400 rounded-lg flex items-center justify-center mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-xs\",\n                                                        children: \"\\uD83D\\uDCA1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        content: question.explanation || '',\n                                                        className: \"text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    onClick: handleNextQuestion,\n                                    isLoading: isLoadingNext,\n                                    loadingText: t('generatingQuestion') || 'Generating...',\n                                    className: \"group bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-600 hover:from-blue-600 hover:via-indigo-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-blue-300/50 transform hover:-translate-y-0.5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: t('nextQuestion')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-5 h-5 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3 h-3 transform group-hover:translate-x-0.5 transition-transform duration-300\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ResultCard.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_s(ResultCard, \"61LWqqzqFNNA/JXDtZGej/exAWM=\", false, function() {\n    return [\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage\n    ];\n});\n_c = ResultCard;\nvar _c;\n$RefreshReg$(_c, \"ResultCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ResultCard.tsx\n"));

/***/ })

});